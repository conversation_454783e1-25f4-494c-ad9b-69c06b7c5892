import { Accordion } from 'solid-bootstrap';
import { RangeTableProps } from '../types';
import { createMemo } from 'solid-js';

const RangeTable = (props: RangeTableProps) => {
  if (!props.data) return;
  const activeKey = createMemo(() => (props.isClosed ? '1' : '0'));

  return (
    <>
      <Accordion
        defaultActiveKey={activeKey()}
        class="accordion accordion-icon-toggle"
        flush
      >
        <Accordion.Item eventKey="0">
          <Accordion.Header class="accordion-header py-3 d-flex">
            Rangos
          </Accordion.Header>
          <Accordion.Body>
            <div class="py-5">
              <p class="mb-10">Unidad: {props.data.unit}</p>
              <div class="table-responsive">
                <table class="table table-row-dashed table-row-gray-300 gy-7">
                  <thead style={'display:none'}>
                    <tr class="fw-bold fs-6 text-gray-800"></tr>
                  </thead>
                  <tbody>
                    <tr>
                      <th>
                        <span
                          style={'vertical-align: inherit;'}
                          class="badge badge-circle badge-info me-5"
                        ></span>
                        <PERSON><PERSON><PERSON>
                      </th>
                      <th>{props.data.min}</th>
                    </tr>
                    <tr>
                      <th>
                        <span
                          style={'vertical-align: inherit;'}
                          class="badge badge-circle badge-info me-5"
                        ></span>
                        Máximo
                      </th>
                      <th>{props.data.max}</th>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </Accordion.Body>
        </Accordion.Item>
      </Accordion>
    </>
  );
};
export default RangeTable;
