/* eslint-disable @typescript-eslint/no-non-null-assertion */
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

type PdfExportOptions = {
  fileName?: string;
  pageSize?: {
    width: number;
    height: number;
  };
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  scale?: number;
};
export class PdfExportService {
  private readonly defaultOptions: PdfExportOptions = {
    fileName: 'exported-document.pdf',
    pageSize: {
      width: 210, // A4 width in mm
      height: 297, // A4 height in mm
    },
    margin: {
      top: 10,
      right: 10,
      bottom: 10,
      left: 10,
    },
    scale: 2,
  };

  async exportToPdf(
    element: HTMLElement,
    options: Partial<PdfExportOptions> = {}
  ): Promise<void> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      const pdf = this.initializePdf(mergedOptions);
      // Store original scroll position
      const originalScrollPos = window.scrollY;
      // Calculate dimensions
      const pageWidth =
        mergedOptions.pageSize!.width -
        (mergedOptions.margin!.left + mergedOptions.margin!.right);
      const pageHeight =
        mergedOptions.pageSize!.height -
        (mergedOptions.margin!.top + mergedOptions.margin!.bottom);
      const elementWidth = element.offsetWidth;
      const elementHeight = element.scrollHeight;
      // Calculate scale to fit width
      const widthScale = pageWidth / elementWidth;
      const scaledHeight = elementHeight * widthScale;
      const totalPages = Math.ceil(scaledHeight / pageHeight);
      // Calculate pixels per page
      const pixelsPerPage = pageHeight / widthScale;
      // Process each page
      for (let pageNum = 0; pageNum < totalPages; pageNum++) {
        if (pageNum > 0) {
          pdf.addPage();
        }
        const isLastPage = pageNum === totalPages - 1;
        const yPosition = pageNum * pixelsPerPage;
        // Calculate height for this page section
        const remainingHeight = elementHeight - yPosition;
        const currentPageHeight = Math.min(pixelsPerPage, remainingHeight);
        // Temporarily modify element to show correct portion
        const originalStyle = element.style.cssText;
        element.style.height = `${elementHeight}px`;
        window.scrollTo(0, yPosition);
        // Ensure the DOM is updated
        await new Promise((resolve) => setTimeout(resolve, 100));
        // Capture the visible portion
        const canvas = await html2canvas(element, {
          scale: mergedOptions.scale,
          windowWidth: elementWidth,
          windowHeight: currentPageHeight,
          x: 0,
          y: yPosition,
          scrollY: yPosition,
          height: currentPageHeight,
          useCORS: true,
          allowTaint: true,
          logging: false,
        });
        // Add to PDF with proper scaling
        const imgData = canvas.toDataURL('image/jpeg', 1.0);
        if (isLastPage) {
          // For the last page, maintain aspect ratio without forcing full height
          const aspectRatio = canvas.width / canvas.height;
          const imageWidth = pageWidth;
          const imageHeight = imageWidth / aspectRatio;
          pdf.addImage(
            imgData,
            'JPEG',
            mergedOptions.margin!.left,
            mergedOptions.margin!.top,
            imageWidth,
            imageHeight
          );
        } else {
          // For other pages, fill the full height
          pdf.addImage(
            imgData,
            'JPEG',
            mergedOptions.margin!.left,
            mergedOptions.margin!.top,
            pageWidth,
            pageHeight
          );
        }
        // Restore original element style
        element.style.cssText = originalStyle;
      }
      // Restore original scroll position
      window.scrollTo(0, originalScrollPos);
      // Save the PDF
      pdf.save(mergedOptions.fileName);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error generating PDF:', error);
      throw error;
    }
  }

  private initializePdf(options: PdfExportOptions): jsPDF {
    return new jsPDF({
      orientation:
        options.pageSize!.width > options.pageSize!.height
          ? 'landscape'
          : 'portrait',
      unit: 'mm',
      format: [options.pageSize!.width, options.pageSize!.height],
    });
  }
}
