import { PreConfiguredChallengeOption } from '../../../apps/challenges/types';
import { Form } from 'solid-bootstrap';
import { ChallengesProgressType } from 'entities/Challenge';
import { Accessor } from 'solid-js';

export const ChallengeType = (props: {
  isEdit: boolean;
  handleChallengeProgressTypeRadioChange: (event: Event) => void;
  challengeProgressType: Accessor<ChallengesProgressType>;
  challengeOptions: PreConfiguredChallengeOption;
}) => {
  const {
    challengeOptions: { PROGRESS_TYPE },
    challengeProgressType,
  } = props;
  const hasToHideOption = () => {
    if (props.isEdit)
      return challengeProgressType() === 'HABIT' && props.isEdit;
    else return false;
  };
  const hasToHideHabitOption = () => {
    if (props.isEdit)
      return challengeProgressType() !== 'HABIT' && props.isEdit;
    else return false;
  };

  return (
    <>
      <Form.Group class="mb-15">
        <Form.Label class="mt-5">Selecciona el tipo de meta</Form.Label>
        <div class="mb-3 mt-8">
          {!hasToHideOption() && PROGRESS_TYPE.FINAL_RESULT && (
            <>
              <Form.Check
                class="progress-type-label mb-3"
                type="radio"
                name="FINAL_RESULT"
                value="FINAL_RESULT"
                label="Alcanzar un objetivo"
                checked={props.challengeProgressType() === 'FINAL_RESULT'}
                onChange={props.handleChallengeProgressTypeRadioChange}
              />
              <p class="progress-type-description ps-10">
                Elige esta opción para llegar a un número que representará tu
                objetivo. Se tomara el último de los registros de progreso
                resultado final de la meta.
              </p>
            </>
          )}
          {!hasToHideOption() && PROGRESS_TYPE.ACCUMULATIVE && (
            <>
              <Form.Check
                class="progress-type-label mb-3 mt-10"
                type="radio"
                name="ACCUMULATIVE"
                value="ACCUMULATIVE"
                label="Acumulativo"
                checked={props.challengeProgressType() === 'ACCUMULATIVE'}
                onChange={props.handleChallengeProgressTypeRadioChange}
              />
              <p class="progress-type-description ps-10">
                Se tomara el último de los registros de progreso resultado final
                de la meta. Se tomaran todos los registros de progreso y se
                usara la suma de cada uno de sus valores como resultado final.
              </p>
            </>
          )}
          {!hasToHideHabitOption() && PROGRESS_TYPE.HABIT && (
            <>
              <Form.Check
                class="progress-type-label mb-3 mt-10"
                type="radio"
                name="HABIT"
                value="HABIT"
                label="Hábito"
                checked={props.challengeProgressType() === 'HABIT'}
                onChange={props.handleChallengeProgressTypeRadioChange}
              />
              <p class="progress-type-description ps-10">
                Elige esta opción para retos que se repiten diariamente. Estos
                retos buscan formar hábitos en las personas y el ganador sera la
                persona con más días completados.
              </p>
            </>
          )}
        </div>
      </Form.Group>
    </>
  );
};
