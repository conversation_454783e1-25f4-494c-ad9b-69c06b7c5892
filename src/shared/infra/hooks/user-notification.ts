import { Accessor, createEffect, createSignal, onMount } from 'solid-js';
import { NotificationAPI } from '../api/notification';
import { Notification } from 'entities/Notification';
import { createStore } from 'solid-js/store';

export function useNotification(props: { refresh: Accessor<boolean> }) {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [error, setError] = createSignal<string | null>(null);
  const [notificationList, setNotificationList] = createStore<Notification[]>(
    []
  );
  const retrieveNotificationList = async () => {
    try {
      setIsLoading(true);
      const result = await new NotificationAPI().getNotifications();
      setNotificationList(result);
    } catch (error) {
      setError('Error obteniendo las notificaciones');
    } finally {
      setIsLoading(false);
    }
  };
  const markNotificationAsRead = async (id: string) => {
    try {
      setIsLoading(true);
      await new NotificationAPI().markAsRead(id);
      updateReadNotification(id);
    } catch (error) {
      setError('Error marcando la notificación como leída');
    } finally {
      setIsLoading(false);
    }
  };
  const deleteNotification = async (id: string) => {
    try {
      setIsLoading(true);
      await new NotificationAPI().archiveNotification(id);
      await retrieveNotificationList();
    } catch (error) {
      setError('Error eliminando la notificación');
    } finally {
      setIsLoading(false);
    }
  };
  const deleteAllNotifications = async () => {
    try {
      setIsLoading(true);
      // Eliminar todas las notificaciones en paralelo
      await Promise.all(
        notificationList.map((notification) =>
          new NotificationAPI().archiveNotification(notification.id)
        )
      );
      await retrieveNotificationList();
    } catch (error) {
      setError('Error eliminando todas las notificaciones');
    } finally {
      setIsLoading(false);
    }
  };
  const updateReadNotification = (id: string) => {
    const updatedNotificationList = notificationList.map((notification) =>
      notification.id === id ? { ...notification, isRead: true } : notification
    );
    setNotificationList(updatedNotificationList);
    setIsLoading(true);
  };
  onMount(() => {
    retrieveNotificationList();
  });
  createEffect(() => props.refresh() && retrieveNotificationList());
  createEffect(() => {
    isLoading() === true && setError(null);
  });

  return {
    isLoading,
    error,
    notificationList,
    markNotificationAsRead,
    deleteNotification,
    deleteAllNotifications,
  };
}
