import { Error<PERSON>lert } from '../../../shared/components/alert/error-alert';
import { LoadingAlert } from '../../../shared/components/alert/loading-alert';
import { useMedicalRecord } from '../../../shared/infra/hooks/use-medical-record';
import { getCurrentUserData } from '../../../shared/services/user/user-session-management';
import { Card, Col, Row, Tab, Tabs } from 'solid-bootstrap';
import { createMemo, For, Match, Show, Switch } from 'solid-js';
import { DataLabel } from '../../../shared/components/data-label';
import UserRecordHeader from './user-record-header';
import { DiagnosisReport } from './diagnosis-report';
import { LabReportTable } from '../../../shared/components/medical-data/lab-report-table';
import EmptyTableIcon from '../../../shared/components/table/empty-table-icon';
import { PDFExportButton } from '../../../shared/components/pdf-export-button';

export const MedicalRecord = () => {
  const user = createMemo(() => getCurrentUserData());
  const { isLoading, error, data } = useMedicalRecord();

  return (
    <>
      <Show when={isLoading()}>
        <Card class="mb-4">
          <LoadingAlert loadingFlags={isLoading} showLoadingAnimation />
        </Card>
      </Show>
      <Show when={!isLoading()}>
        <ErrorAlert error={error} />
        <Switch>
          <Match when={data !== null}>
            <Row>
              <Col sm={12} md={12} lg={3}>
                <UserRecordHeader user={user} />
                <Card class="mb-4">
                  <Card.Header
                    class="border-0 pt-6"
                    style={{
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      'min-height': '50px',
                    }}
                  >
                    <div class="card-title m-0">
                      <h3 class="fw-bold m-0">Datos Generales </h3>
                    </div>
                  </Card.Header>
                  <Card.Body class="mt-2 py-2">
                    <DataLabel label={'Nombre'} value={data.basicInfo.name} />
                    <DataLabel
                      label={'Cédula'}
                      value={data.basicInfo.documentId}
                    />
                    <DataLabel
                      label={'Edad'}
                      value={data.basicInfo.age.toString()}
                    />
                    <DataLabel
                      label={'Fecha de Nacimiento'}
                      value={data.basicInfo.birthDate}
                    />
                    <DataLabel
                      label={'Altura'}
                      value={`${data.basicInfo.height.toString()} m`}
                    />
                    <DataLabel
                      label={'Peso'}
                      value={`${data.basicInfo.weight.toString()} kg`}
                    />
                    <DataLabel
                      label={'IMC'}
                      value={data.basicInfo.BMI.toString()}
                    />
                  </Card.Body>
                </Card>
                <Card class="mb-4">
                  <Card.Header
                    class="border-0 pt-6"
                    style={{
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      'min-height': '50px',
                    }}
                  >
                    <div class="card-title m-0">
                      <h3 class="fw-bold m-0">Información de Contacto </h3>
                    </div>
                  </Card.Header>
                  <Card.Body class="mt-2 py-2">
                    <DataLabel
                      label={'Teléfono'}
                      value={data.contactInfo.phone.toString()}
                    />
                    <DataLabel
                      label={'Correo Electrónico'}
                      value={data.contactInfo.email}
                    />
                  </Card.Body>
                </Card>
                <Card class="mb-4">
                  <Card.Header
                    class="border-0 pt-6"
                    style={{
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      'min-height': '50px',
                    }}
                  >
                    <div class="card-title m-0">
                      <h3 class="fw-bold m-0">Ubicación </h3>
                    </div>
                  </Card.Header>
                  <Card.Body class="mt-2 py-2">
                    <DataLabel label={'País'} value={data.basicInfo.country} />
                  </Card.Body>
                </Card>
              </Col>
              <Col sm={12} md={12} lg={9}>
                <Tabs
                  defaultActiveKey="lab"
                  class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x border-0 fs-4 fw-semibold mb-8"
                >
                  <Tab eventKey="lab" title="Laboratorio">
                    <Card>
                      <Card.Body>
                        <Show when={data.labReport}>
                          <div class="float-end">
                            <PDFExportButton
                              elementSelector="#lab-report"
                              fileName="expediente-medico.pdf"
                            />
                          </div>
                          <div id="lab-report">
                            <For each={data.labReport.data || []}>
                              {(healthData) => {
                                return (
                                  <div class="my-5">
                                    <LabReportTable data={healthData} />
                                  </div>
                                );
                              }}
                            </For>
                          </div>
                        </Show>
                      </Card.Body>
                    </Card>
                  </Tab>
                  <Tab eventKey="observations" title="Observaciones">
                    <Card>
                      <Card.Header class="border-0">
                        <div class="card-title m-0 mt-8 mb-5">
                          <h3 class="fw-bold m-0">Últimos Diagnósticos </h3>
                        </div>
                      </Card.Header>
                      <Card.Body class="py-0">
                        <Show when={data.labReport.data.length === 0}>
                          <div class="my-10">
                            <p>No se encontraron datos de diagnósticos.</p>
                          </div>
                        </Show>
                        <Show when={data.labReport}>
                          <div>
                            <For each={data.labReport.data || []}>
                              {(healthData) => {
                                return (
                                  <div class="my-5">
                                    <DiagnosisReport data={healthData} />
                                  </div>
                                );
                              }}
                            </For>
                          </div>
                        </Show>
                      </Card.Body>
                    </Card>
                  </Tab>
                </Tabs>
              </Col>
            </Row>
          </Match>
          <Match when={data === null}>
            <Card class="py-10">
              <Row>
                <EmptyTableIcon class="m-auto" style="height:200px" />
                <label class="m-auto mt-8 text-center mb-20">
                  No se encontraron datos
                </label>
              </Row>
            </Card>
          </Match>
        </Switch>
      </Show>
    </>
  );
};
