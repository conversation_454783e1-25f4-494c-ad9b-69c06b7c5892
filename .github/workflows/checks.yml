name: Run checks
on:
  pull_request:
    # Sequence of patterns matched against refs/heads
    branches:
      - main
      - stg
      - dev

jobs:
  checks:
    name: PR Checks
    runs-on: ubuntu-latest
    env:
      AWS_REGION : ${{ secrets.AWS_REGION }}
      AWS_S3_BUCKET_NAME : ${{ secrets.AWS_S3_BUCKET_NAME }}
      AWS_ACCESS_KEY_ID : ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY : ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      VITE_POOL_PROD: ${{ secrets.VITE_POOL_PROD }}
      VITE_POOL_DEV: ${{ secrets.VITE_POOL_DEV }}
      VITE_CLIENT_ID_PROD: ${{ secrets.VITE_CLIENT_ID_PROD }}
      VITE_CLIENT_ID_DEV: ${{ secrets.VITE_CLIENT_ID_DEV }}
      VITE_ENV: ${{ secrets.VITE_ENV_DEV }}

    steps:
      - uses: AutoModality/action-clean@v1
      - name: ⬇️ Checkout repo
        uses: actions/checkout@v3
      - uses: actions/cache@v4
        with:
          path: "**/node_modules"
          key: ${{ runner.os }}-modules-${{ hashFiles('**/package-lock.json') }}

      - name: 📥 Install modules
        run: npm install

      - name: 🔎 Typecheck
        run: npm run typecheck

      - name: 🔦 Lint Code
        run: npm run lint

      - name: 🔥 Build
        run: npm run build

      - name: 🧻 Cleaning workspace
        uses: AutoModality/action-clean@v1
        if: ${{ always() }}
