import { Component, createSignal } from 'solid-js';
import { PdfExportService } from '../services/utils/pdf-service';
import { Button } from 'solid-bootstrap';

type PDFButtonProps = {
  elementSelector: string;
  fileName?: string;
  buttonLabel?: string;
  margin?: number;
  scale?: number;
};
export const PDFExportButton: Component<PDFButtonProps> = ({
  elementSelector,
  fileName = 'document.pdf',
  buttonLabel = 'Exportar a PDF',
  scale = 2,
}) => {
  const [isDisabled, setIsDisabled] = createSignal(false);
  const pdfExportService = new PdfExportService();
  const handleExport = async () => {
    setIsDisabled(true);
    const element = document.querySelector(elementSelector);
    if (!element) {
      // eslint-disable-next-line no-console
      console.error('Element Not Found:', elementSelector);

      return;
    }
    try {
      await pdfExportService.exportToPdf(element as HTMLElement, {
        fileName,
        scale,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error Exporting PDF:', error);
    } finally {
      setIsDisabled(false);
    }
  };

  return (
    <Button
      onClick={handleExport}
      disabled={isDisabled()}
      class="btn btn-primary"
    >
      {buttonLabel}
    </Button>
  );
};
